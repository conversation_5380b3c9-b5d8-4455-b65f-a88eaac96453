<script setup>
import {ref, onMounted} from 'vue'
import {Grid, GridItem, Cell, CellGroup, Picker, Popup, Field, Image, Search} from 'vant';
import Logo from '@/assets/imgs/schoolOrg/logo.png'
import axios from "@/utils/request";
import {useRouter} from "vue-router";
const router = useRouter()

const fieldValue = ref('');
const showPicker = ref(false);
const searchVal = ref('');
const result = ref(0)

onMounted(() => {
  const isLogin = localStorage.getItem('isLogin')
  if (isLogin && isLogin === '1'){
    axios.post('/alumni/mobile/memberNum').then(res => {
      console.log('query memberNum res=', res)
      if (res.data.success){
        result.value = res.data.result
      }
    })
  }else{
    router.push('/login')
  }
})

function onSearch(value) {
  searchVal.value = value
  // router.push('/')
  // current.userList = []
  // page.pageNo = 0
  // finished.value = false;
  // onLoad()
}

</script>

<template>
  <div class="container">
    <Search
        shape="round"
        v-model="searchVal"
        placeholder="全局搜索"
        @search="onSearch">
    </Search>
    <div class="list">
      <div class="item">
        <Image :src="Logo" height="60" width="60"/>
        <div class="item-right">
          <span class="title">兰溪一中校友总会</span>
          <span class="count">成员 {{ result }}</span>
        </div>
      </div>
<!--      <div class="item">-->
<!--        <Image :src="Logo" height="60" width="60"/>-->
<!--        <div class="item-right">-->
<!--          <span class="title">兰溪一中校友总会</span>-->
<!--          <span class="count">成员 134</span>-->
<!--        </div>-->
<!--      </div>-->
    </div>
  </div>
</template>

<style scoped lang="postcss">
.container{
  .list{
    display: flex;
    //align-items: center;
    flex-flow: column;
    //padding: 12px;
    //row-gap: 12px;
    .item{
      display: flex;
      border-bottom: 1px solid #ECECEC;
      padding: 18px 12px;
      .item-right{
        display: flex;
        flex-flow: column;
        padding-left: 12px;
        row-gap: 6px;
        .title{
          font-size: 18px;
          font-weight: bold;
        }
        .count{
          font-size: 16px;
          color: #7B8293;
        }
      }
    }
  }
}
</style>
