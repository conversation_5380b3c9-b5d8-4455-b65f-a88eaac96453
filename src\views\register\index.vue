<script setup>
import {ref, onMounted} from 'vue'
import {Grid, GridItem, Cell, CellGroup, Picker, Popup, Field, Image} from 'vant';
import RegisterForm from "@/components/registerForm.vue";
import {useRouter} from "vue-router";

const fieldValue = ref('');
const showPicker = ref(false);
const router = useRouter()

onMounted(()=>{
  const _isLogin = localStorage.getItem('isLogin')
  const _isRegister = localStorage.getItem('isRegister');
  if (_isLogin && _isLogin === '1'){
    if (_isRegister && _isRegister === '1'){
    }else{
      router.push('/register')
    }
  }else{
    router.push('/login')
  }
})
</script>

<template>
  <div class="container">
    <RegisterForm />
  </div>
</template>

<style scoped lang="postcss">

</style>
