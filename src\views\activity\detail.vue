<script setup>
import {ref, onMounted, reactive} from 'vue'
import {Grid, Button, Cell, CellGroup, Picker, Lazyload, Field, Image, showNotify, Dialog, Loading} from 'vant';
import {useRouter, useRoute} from "vue-router";
import coverImg from '@/assets/imgs/activity/cover.png'
import img1 from '@/assets/imgs/activity/img1.png'
import img2 from '@/assets/imgs/activity/img2.png'
import img3 from '@/assets/imgs/activity/img3.png'
import btnOkImg from '@/assets/imgs/activity/btnOk.png'

import paymentCodeImg from '@/assets/imgs/activity/paymentCode.jpg'
import axios from "@/utils/request";
import qs from 'qs'
import dayjs from "dayjs";
const showDlg = ref(false)

const userInfo = reactive({})
const detailId = ref('');
const showPicker = ref(false);
const router = useRouter()
const route = useRoute()
const activityDetailInfo = reactive({})
const loading = ref(true)

onMounted(() => {
  console.log(router); // 输出 "abc"
  const _userInfo = localStorage.getItem("user")
  if (_userInfo) {
    Object.assign(userInfo, JSON.parse(_userInfo))
  }else{
    localStorage.removeItem('isLogin')
    // localStorage.removeItem('phone')
    localStorage.removeItem('isRegister')
    router.push('/login')
    return
  }
  // const idParam = route.query.id
  if (route.query.id) {
    detailId.value = route.query.id
    getDetailById(route.query.id, userInfo.id)
  }
  let input = document.querySelector('input');
  if (input){
    input.type = 'search';
  }
  // console.log(idParam); // 输出 "abc"
})

function getDetailById() {
  loading.value = true
  let detailId = ''
  if (route.query.id) {
    detailId = route.query.id
  }
  if (detailId && userInfo.id) {
    let params = {
      activityId: detailId,
      userId: userInfo.id
    }
    axios.post('/alumni/mobile/activityDetail', qs.stringify(params)).then(res => {
      loading.value = false
      console.log('activityDetail res=', res)
      if (res.data.success) {
        if (res.data.result) {
          Object.assign(activityDetailInfo, res.data.result)
        }
      }
    })
  }
}

function getYuan() {
  if (activityDetailInfo.treatmentMoney === 0) {
    return '免费'
  } else if (activityDetailInfo.isDutch) {
    return `￥${activityDetailInfo.treatmentMoney}（AA制）`
  } else {
    return `￥${activityDetailInfo.treatmentMoney}`
  }
}

function getStatusClass(item) {
  if (activityDetailInfo.status === 0) {
    return 'top-right-green'
  }
  if (activityDetailInfo.status === 1) {
    return 'top-right-red'
  }
  if (activityDetailInfo.status === -1) {
    return 'top-right-gray'
  }
}

function getStatusName(item) {
  if (activityDetailInfo.status === 0) {
    return '报名中'
  }
  if (activityDetailInfo.status === 1) {
    return '进行中'
  }
  if (activityDetailInfo.status === -1) {
    return '已结束'
  }
}

function btnRegister() {
  if (!activityDetailInfo.hasApply) {
    if (userInfo.id) {
      let params = {
        activityId: activityDetailInfo.id,
        userId: userInfo.id
      }
      axios.post('/alumni/mobile/apply', qs.stringify(params)).then(res => {
        console.log('apply res =', res)
        if (res.data.success) {
          showDlg.value = true
          // showNotify({type: 'success', message: '报名成功'})
          // getDetailById()
        } else if (res.data.message) {
          showNotify({type: 'primary', message: res.data.message})
        }
      })

      const extra = {"id": activityDetailInfo.id}
      let params2 = {
        "action": "click",
        "event": "activity_item_apply",
        // 活动id
        "extra": JSON.stringify(extra),
        "module": "activity",
        "pageUrl": "/activity",
        "userId": userInfo.id
      }
      axios.post('/alumni/mobile/eventReport', params2).then(res => {
        console.log('eventReport res=', res)
      })
    }
    // if (!isDue(activityDetailInfo)) {
    //
    // } else {
    //   showNotify({type: 'primary', message: '报名已截止'})
    // }
  } else {
    showNotify({type: 'primary', message: '您已报名'})
  }
}

function isDue(item) {
  console.log('is due item =', item.signInDeadline)
  const s = dayjs().isAfter(dayjs(item.signInDeadline))
  console.log('result=', s)
  return s
}

function btnOk() {
  showDlg.value = false
  getDetailById()
}
</script>

<template>
  <div class="container">

    <div v-if="activityDetailInfo.id">
      <div class="detail-up">
        <div class="item">
          <div class="header">
            <Image :src="activityDetailInfo.activityCoverImageUrl" show-loading lazy-load
                   radius="12px 12px 0 0"></Image>
            <div :class="['top-right', getStatusClass()]">
              {{ getStatusName() }}
            </div>
            <!--            <div class="bottom-left">-->
            <!--              免费-->
            <!--            </div>-->
          </div>
          <div class="footer">
            <h3 class="title">{{ activityDetailInfo.activityName }}</h3>
            <div class="infos">
              <div class="info">
                <div class="img">
                  <Image :src="img1" width="18" height="18"/>
                </div>
                <span>举办日期：{{ activityDetailInfo.activityDate }}</span>
              </div>
              <div class="info">
                <div class="img">
                  <Image :src="img2" width="16"/>
                </div>
                <span>举办地址：{{ activityDetailInfo.activityAddress }}</span>
              </div>
              <div class="info">
                <div class="img">
                  <Image :src="img3" width="16"/>
                </div>
                <span class="yuan">{{ getYuan() }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="detail-down">
        <div class="header">
          <span class="title">活动详情</span>
        </div>
        <p class="intro" v-html="activityDetailInfo.activityRemark">
        </p>
<!--        <div v-if="activityDetailInfo.treatmentMoney>0" class="payment-code">-->
<!--          <Image :src="paymentCodeImg" radius="8"/>-->
<!--        </div>-->
        <div class="btn" v-if="[0,1].includes(activityDetailInfo.status) && !isDue(activityDetailInfo)">
          <Button block :type="activityDetailInfo.hasApply? 'danger':'primary'" round :plain="activityDetailInfo.hasApply" @click.stop="()=>btnRegister(activityDetailInfo)">
            {{ activityDetailInfo.hasApply ? '您已报名该活动' : '我要报名'}}
          </Button>
        </div>
      </div>
    </div>
    <div class="loading" v-else>
      <Loading size="18"/>
      <span class="lod-txt">加载中...</span>
    </div>
    <Dialog v-model:show="showDlg" :showConfirmButton="false">
      <div class="dlg-body">
        <Image :src="btnOkImg" width="80"/>
        <div class="mid">恭喜您，报名成功</div>
        <Button block type="primary" round @click="btnOk">我知道了</Button>
      </div>
    </Dialog>
  </div>
</template>

<style scoped lang="postcss">
.container {
  padding-bottom: 100px;
  .dlg-body{
    padding: 40px 30px 40px;
    display: flex;
    align-items: center;
    row-gap: 12px;
    flex-flow: column;
    font-size: 20px;
    font-weight: bold;
    .mid{
      padding-top: 10px;
      padding-bottom: 30px;
    }
  }
  .loading {
      display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px;
    .lod-txt{
      color: #969799;
      padding-left: 6px;
    }
  }

  .detail-down {
    border-top: 12px solid #EFEFEF;

    .header {
      padding: 12px;
      border-bottom: 1px solid #EFEFEF;

      .title {
        font-size: 16px;
        border-left: 3px solid #1D94F4;
        padding-left: 12px;
      }
    }

    .intro {
      font-size: 16px;
      padding: 12px 20px;
      text-indent: 2em;
      line-height: 30px;
    }

    .payment-code {
      padding: 0 20px;
      border-radius: 12px;
    }

    .btn {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 12px;
      background: #fff;
    }
  }

  .detail-up {
    padding: 12px;

    .item {
    //box-shadow: 0 2px 6px rgba(0, 0, 0, .15); border-radius: 12px; //margin-bottom: 20px;

      .footer {
        padding: 12px;

        .title {
          padding-bottom: 12px;
        }

        .infos {
          display: flex;
          flex-flow: column;
          row-gap: 6px;

          .info {
            font-size: 16px;
            color: #72798C;
            display: flex;
            align-items: center;
            column-gap: 12px;

            .yuan {
              color: #E05964;
              font-size: 16px;
              font-weight: 500;
            }

            .img {
              width: 26px;
              height: 26px;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;
            }
          }
        }

      }

      .header {
        position: relative;

        .bottom-left {
          background: #E15B66;
          position: absolute;
          bottom: 12px;
          left: 12px;
          padding: 6px 12px;
          color: #fff;
          font-size: 16px;
        }

        .top-right {
          position: absolute;
          top: 0;
          right: 0;
          padding: 6px 12px;
          border-radius: 12px 12px 0 12px;
          color: #fff;
          font-size: 16px;
        }

        .top-right-green {
          background: #00991F;
        }

        .top-right-red {
          background: #E15B66;
        }

        .top-right-gray {
          background: #848A9B;
        }
      }
    }
  }
}
</style>
