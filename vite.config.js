import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import postNesting from 'postcss-nesting'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
      // 其他别名...
    }
  },
  server: {
    proxy: {
      "/api": {
        target: "http://alumni.hypersmart.ltd",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ""),
      },
    },
  },
  plugins: [vue()],
  css: {
    postcss: {
      plugins: [
        postNesting()
      ]
    }
  }
})
