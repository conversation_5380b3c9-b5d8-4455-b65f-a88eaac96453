<script setup>
import {ref, onMounted, reactive} from 'vue'
import {Grid, GridItem, Cell, CellGroup, Picker, Popup, Field, Image,Button,Empty} from 'vant';
import Avatar from '@/assets/imgs/userinfo/avatar.png'
import RegisterForm from "@/components/registerForm.vue";
import axios from "@/utils/request";
import {useRouter} from "vue-router";
import mao from '@/assets/imgs/userinfo/mao.png'
const router = useRouter()

const fieldValue = ref('');
const showPicker = ref(false);
const userData = reactive({
  data: null
})
const isRegister = ref(false)

onMounted(()=>{
  const _isLogin = localStorage.getItem('isLogin')
  const _isRegister = localStorage.getItem('isRegister');

  if (_isLogin && _isLogin === '1'){

  }else{
    router.push('/login')
  }

  isRegister.value = !!(_isRegister && _isRegister === '1');

  if(!isRegister.value){
    router.push('/register')
  }

  const phone = localStorage.getItem('phone')
  if (phone){
    axios.get('/alumni/mobile/queryByMobile?phone='+phone).then(res => {
      console.log('query user info res=', res)
      if (res.data.success){
        userData.data = res.data.result
      }
    })
  }
})

function getPostName() {
  if (userData.data){
    return `${userData.data.company || ''} ${userData.data.post || ''}`
  }
  return '-'
}

function btnQuit() {
  localStorage.removeItem('isLogin')
  // localStorage.removeItem('phone')
  localStorage.removeItem('isRegister')
  router.push('/login')
}

</script>

<template>
  <div class="container">
    <div class="card-outer">
      <div class="card">
        <div class="fixed">
          <Image :src="mao" height="80"/>
        </div>
        <div class="avatar">
          <Image :src="Avatar" width="60" height="60" round/>
        </div>
        <div class="card-info">
          <span class="name">{{ userData.data && userData.data.userName || '-' }}</span>
          <span class="label">{{ getPostName() }}</span>
        </div>
        <div class="fixed-btn">
          <div class="quit" @click="btnQuit">退出登录</div>
        </div>
      </div>
    </div>
    <div>
      <RegisterForm :showCover="false" :userData="userData.data" v-if="isRegister"/>
      <div v-else class="empty">
        <Empty description="暂无数据" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="postcss">
.empty{
  font-size: 18px;
  width: 100%;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ddd;
}
.card-outer{
  padding: 20px 12px;
  //background: linear-gradient(to top, #fff 0%, #FFCCD1 100%);

}
.card{
  background: linear-gradient(135deg, #1D94F4 0%, #1D94F4 100%);
  display: flex;
  padding: 20px 12px;
  border-radius: 12px 12px 0 0;
  column-gap: 12px;
  position: relative;
  .fixed{
    position: absolute;
    bottom: 0;
    right: 0;
  }
  .fixed-btn{
    position: absolute;
    right: 12px;
    top: 40%;
    .quit{
      background: #fff;
      color: #333;
      padding: 6px 18px;
      border-radius: 18px;
    }
  }
  .avatar{

  }
  .card-info{
    display: flex;
    flex-flow: column;
    row-gap: 2px;
    .name{
      font-size: 18px;
      color: #fff;
      font-weight: bold;
    }
    .label{
      font-size: 16px;
      color: #fff;
      max-width: 160px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

    }
  }
}
</style>
