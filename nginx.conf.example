# Nginx配置示例 - 用于生产环境部署
# 将此配置放在您的nginx服务器配置中

server {
    listen 80;
    server_name h5.alumnni.hypersmart.ltd;
    
    # 前端静态文件
    location / {
        root /path/to/your/dist;
        try_files $uri $uri/ /index.html;
        
        # 添加CORS头（如果需要）
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,X-Access-Token';
    }
    
    # API代理
    location /api/ {
        # 移除/api前缀并转发到后端
        rewrite ^/api/(.*)$ /$1 break;
        
        proxy_pass http://alumni.hypersmart.ltd;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS配置
        proxy_set_header Origin http://h5.alumnni.hypersmart.ltd;
        
        # 处理预检请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' 'http://h5.alumnni.hypersmart.ltd';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,X-Access-Token';
            add_header 'Access-Control-Allow-Credentials' 'true';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
        
        # 添加CORS头到实际响应
        add_header 'Access-Control-Allow-Origin' 'http://h5.alumnni.hypersmart.ltd' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,X-Access-Token' always;
    }
}
