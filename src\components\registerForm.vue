<script setup>
import {Area, Button, CellGroup, Field, Image, Picker, Popup, showDialog, showNotify, RadioGroup, Radio} from 'vant';
import dayjs from "dayjs";
import {onMounted, reactive, ref, watch} from "vue";
import cover from '@/assets/imgs/register/cover.png'
import axios from "@/utils/request";
import {areaList} from '@vant/area-data';
import router from "@/router.js";
import _ from 'lodash'

let userForm = reactive({
  schoolName: '浙江兰溪第一中学',
  userName: '',
  sex: '',
  phone: '',
  birthday: '',
  yearOfGraduation: '',
  class: '',
  gradeType: '高中部',
  hometownCity: '浙江省/金华市/兰溪市',
  hometown: '',
  schoolOfGraduation: '',
  highestEducation: '本科',
  workUnit: '',
  workPost: '',
  // 从事领域
  engagedInField: '',
  workArea: '上海市',
  workAddress: '',
  residentialAddress: '上海市',
  isJoin: '',
  other: '',
})

let hometownCityCode = ref('')

let areaPicker = reactive({
  key: 1,
  show: false,
  type: '',
  val: []
});

// let pickerVal = ref([])
// let showPicker = ref(false)
let picker = reactive({
  key: 1,
  show: false,
  type: '',
  val: [],
  columns: []
})

const props = defineProps({
  showCover: {
    type: Boolean,
    default: true
  },
  userData: {
    type: Object,
    default: null
  }
})

let loading = ref(false)

onMounted(() => {
  const phone = localStorage.getItem('phone')
  if (phone){
    userForm.phone = phone
  }
})
// 监听数据变化
watch(() => props.userData, (userData) => {
  console.log('msg changed to: ', userData.userName)
  if (userData){
    userForm = Object.assign(userForm, {
      schoolName: '浙江兰溪第一中学',
      userName: userData.userName,
      sex: userData.sex,
      phone: userData.phone,
      birthday: userData.birth,
      yearOfGraduation: userData.graduateYear,
      class: userData.classNo,
      gradeType: userData.gradeType,
      hometown: userData.hometownAddress,
      hometownCity: userData.hometownCity,
      schoolOfGraduation: userData.educationalBackground,
      highestEducation: userData.highestEducation,
      workUnit: userData.company,
      workPost: userData.post,
      // 从事领域
      engagedInField: userData.areaExpertise,
      workAddress: userData.companyAddress,
      workArea: userData.companyCity,
      residentialAddress: userData.city,
      // isJoin: userData.present,
      other: userData.advice
    })
    if (userData.id){
      userForm.id = userData.id
    }
  }
})

function onConfirmArea({ selectedOptions }) {
  console.log('area val=', areaPicker.val)
  console.log('selectedOptions =', selectedOptions)
  areaPicker.show = false;
  if (areaPicker.type === 'hometownCity'){
    userForm.hometownCity = selectedOptions.map((item) => item.text).join('/');
    hometownCityCode.value = selectedOptions[2].value
    userForm.hometown = ''
  }
  if (areaPicker.type === 'residentialAddress'){
    userForm.residentialAddress = selectedOptions.map((item) => item.text).join('/');
    if (!userForm.workArea){
      userForm.workArea = selectedOptions.map((item) => item.text).join('/');
    }
  }
  if (areaPicker.type === 'workArea'){
    userForm.workArea = selectedOptions.map((item) => item.text).join('/');
    if (!userForm.residentialAddress){
      userForm.residentialAddress = selectedOptions.map((item) => item.text).join('/');
    }
  }
}

function onConfirm({ selectedOptions }) {
  console.log('selectedValues=', selectedOptions)
  picker.show = false
  if (picker.type === 'sex'){
    userForm.sex = selectedOptions[0].text;
  }
  if (picker.type === 'birthday'){
    userForm.birthday = selectedOptions.map((item) => item.text).join('-');
    if (!userForm.yearOfGraduation){
      if (userForm.gradeType){
        if (userForm.gradeType === '初中部'){
          userForm.yearOfGraduation = parseInt(selectedOptions[0].text) + 15;
        }
        if (userForm.gradeType === '高中部'){
          userForm.yearOfGraduation = parseInt(selectedOptions[0].text) + 18;
        }
      }
    }
  }
  if (picker.type === 'yearOfGraduation'){
    userForm.yearOfGraduation = selectedOptions[0].text;
  }
  if (picker.type === 'class'){
    userForm.class = selectedOptions[0].text;
  }
  if (picker.type === 'isJoin'){
    userForm.isJoin = selectedOptions[0].text;
  }
  if (picker.type === 'highestEducation'){
    userForm.highestEducation = selectedOptions[0].text;
  }
  if (picker.type === 'engagedInField'){
    userForm.engagedInField = selectedOptions.map((item) => item.text).join('-');
  }
  if (picker.type === 'hometown'){
    userForm.hometown = selectedOptions[0].text;
  }
  if (picker.type === 'gradeType'){
    userForm.gradeType = selectedOptions[0].text;
    if (!userForm.yearOfGraduation){
      if (userForm.birthday){
        if (userForm.gradeType === '初中部'){
          userForm.yearOfGraduation = parseInt(selectedOptions[0].text) + 15;
        }
        if (userForm.gradeType === '高中部'){
          userForm.yearOfGraduation = parseInt(selectedOptions[0].text) + 18;
        }
      }
    }
  }
}

function onCancel() {
  console.log('onCancel')
  picker.show = false
}

function clickArea(type) {
  areaPicker.key = Date.now()
  areaPicker.show = true
  areaPicker.type = type

  if (type === 'hometownCity'){
    if (userForm.hometownCity){
      const s = userForm.hometownCity.split('/')
      const key1 = Object.keys(areaList.province_list).find(k => areaList.province_list[k] === s[0])
      // const key2 = Object.keys(areaList.city_list).find(k => areaList.city_list[k] === s[1])
      // const key3 = Object.keys(areaList.county_list).find(k => areaList.county_list[k] === s[2])
      // console.log('[key1, key2, key3]=', [key1, key2, key3])
      areaPicker.val = key1
    }
  }
  if (type === 'workArea'){
    if (userForm.workArea){
      const s = userForm.workArea.split('/')
      const key1 = Object.keys(areaList.province_list).find(k => areaList.province_list[k] === s[0])
      // const key2 = Object.keys(areaList.city_list).find(k => areaList.city_list[k] === s[1])
      // const key3 = Object.keys(areaList.county_list).find(k => areaList.county_list[k] === s[2])
      // console.log('[key1, key2, key3]=', [key1, key2, key3])
      areaPicker.val = key1
    }
  }
  if (type === 'residentialAddress'){
    if (userForm.residentialAddress){
      const s = userForm.residentialAddress.split('/')
      const key1 = Object.keys(areaList.province_list).find(k => areaList.province_list[k] === s[0])
      // const key2 = Object.keys(areaList.city_list).find(k => areaList.city_list[k] === s[1])
      // const key3 = Object.keys(areaList.county_list).find(k => areaList.county_list[k] === s[2])
      // console.log('[key1, key2, key3]=', [key1, key2, key3])
      areaPicker.val = key1
    }
  }
}

function clickPicker(type) {
  if (type === 'sex'){
    picker.val = [userForm.sex]
    picker.columns = [{
      text: '男',
      value: '男'
    },{
      text: '女',
      value: '女'
    }]

    picker.key = Date.now();
    picker.show = true
    picker.type = type
  }else if (type === 'birthday'){
    if (!userForm.birthday){
      picker.val = [1980]
    }else{
      // console.log('-0-0-0-0-0', userForm.birthday.split('-'))
      const s = userForm.birthday.split('-')
      picker.val = [parseInt(s[0]), parseInt(s[1])]
    }

    picker.columns = []
    const year = parseInt(dayjs().format('YYYY'))
    let a1 = []
    let a2 = [{
      text: '01',
      value: 1
    },{
      text: '02',
      value: 2
    },{
      text: '03',
      value: 3
    },{
      text: '04',
      value: 4
    },{
      text: '05',
      value: 5
    },{
      text: '06',
      value: 6
    },{
      text: '07',
      value: 7
    },{
      text: '08',
      value: 8
    },{
      text: '09',
      value: 9
    },{
      text: '10',
      value: 10
    },{
      text: '11',
      value: 11
    },{
      text: '12',
      value: 12
    }]
    for (let i = 1920; i <year; i++) {
      a1.push({
        text: i,
        value: i
      })
    }
    picker.columns = [a1, a2]

    picker.key = Date.now();
    picker.show = true
    picker.type = type
  }else if (type === 'yearOfGraduation'){
    picker.columns = []
    const year = parseInt(dayjs().format('YYYY'))
    for (let i = 1933; i <year; i++) {
      picker.columns.push({
        text: i,
        value: i
      })
    }
    picker.val = [parseInt(userForm.yearOfGraduation)]

    picker.key = Date.now();
    picker.show = true
    picker.type = type
  }else if (type === 'class'){
    picker.val = [parseInt(userForm.class)]
    picker.columns = []
    for (let i = 1; i <19; i++) {
      picker.columns.push({
        text: i,
        value: i
      })
    }

    picker.key = Date.now();
    picker.show = true
    picker.type = type
  }else if (type === 'isJoin'){
    picker.val = [userForm.isJoin]
    picker.columns = [{
      text: '是',
      value: '是'
    },{
      text: '否',
      value: '否'
    }]

    picker.key = Date.now();
    picker.show = true
    picker.type = type
  }else if (type === 'highestEducation'){
    picker.val = [userForm.highestEducation]
    picker.columns = [{
      text: '高中/中专',
      value: '高中/中专'
    },{
      text: '大专',
      value: '大专'
    },{
      text: '本科',
      value: '本科'
    },{
      text: '硕士',
      value: '硕士'
    },{
      text: '博士',
      value: '博士'
    }]

    picker.key = Date.now();
    picker.show = true
    picker.type = type
  }else if (type === 'engagedInField'){
    picker.columns = [{
      text: '工业领域',
      value: '工业领域',
      children: [{
        text: '制造业',
        value: '制造业'
      }, {
        text: '电子信息工程',
        value: '电子信息工程'
      }, {
        text: '建筑工程',
        value: '建筑工程'
      }, {
        text: '其他',
        value: '其他'
      }]
    }, {
      text: '服务业领域',
      value: '服务业领域',
      children: [{
        text: '金融',
        value: '金融'
      }, {
        text: '商业服务',
        value: '商业服务'
      }, {
        text: '银行',
        value: '银行'
      }, {
        text: '证券',
        value: '证券'
      }, {
        text: '会计',
        value: '会计'
      }, {
        text: '广告',
        value: '广告'
      }, {
        text: '旅游',
        value: '旅游'
      }, {
        text: '其他',
        value: '其他'
      }]
    }, {
      text: '科技领域',
      value: '科技领域',
      children: [{
        text: '计算机软件和硬件',
        value: '计算机软件和硬件'
      }, {
        text: '通信',
        value: '通信'
      }, {
        text: '生物医药',
        value: '生物医药'
      }, {
        text: '电子商务',
        value: '电子商务'
      }, {
        text: '生物工程',
        value: '生物工程'
      }, {
        text: '其他',
        value: '其他'
      }]
    }, {
      text: '教育领域',
      value: '教育领域',
      children: [{
        text: '教育管理',
        value: '教育管理'
      }, {
        text: '教育和培训',
        value: '教育和培训'
      }, {
        text: '其他',
        value: '其他'
      }]
    }, {
      text: '政府部门',
      value: '政府部门',
      children: [{
        text: '公务员',
        value: '公务员'
      }, {
        text: '其他',
        value: '其他'
      }]
    }, {
      text: '文化传媒领域',
      value: '文化传媒领域',
      children: [{
        text: '新闻出版',
        value: '新闻出版'
      }, {
        text: '广播电视',
        value: '广播电视'
      }, {
        text: '文化艺术',
        value: '文化艺术'
      }, {
        text: '文化传媒',
        value: '文化传媒'
      }, {
        text: '其他',
        value: '其他'
      }]
    }, {
      text: '医疗卫生领域',
      value: '医疗卫生领域',
      children: [{
        text: '医院医务人员',
        value: '医院医务人员'
      }, {
        text: '疾控中心工作人员',
        value: '疾控中心工作人员'
      }, {
        text: '其他',
        value: '其他'
      }]
    }, {
      text: '社会工作领域',
      value: '社会工作领域',
      children: [{
        text: '社会工作者',
        value: '社会工作者'
      }, {
        text: '志愿者工作',
        value: '志愿者工作'
      }, {
        text: '其他',
        value: '其他'
      }]
    }, {
      text: '农林牧渔领域',
      value: '农林牧渔领域',
      children: [{
        text: '农、林、牧、渔业生产',
        value: '农、林、牧、渔业生产'
      }, {
        text: '其他',
        value: '其他'
      }]
    }, {
      text: '其他',
      value: '其他',
      children: [{
        text: '其他',
        value: '其他'
      }]
    }]
    picker.val = userForm.engagedInField.split('-')

    picker.key = Date.now();
    picker.show = true
    picker.type = type
  }else if (type === 'hometown'){
    console.log('areaList=', areaList)
    // areaList.city_list.filter(item => item)
    if (!hometownCityCode.value){
      if (userForm.hometownCity){
        const s = userForm.hometownCity.split('/')
        const key = Object.keys(areaList.county_list).find(k => areaList.county_list[k] === s[2])
        console.log('key====', key)
        hometownCityCode.value= key
      }
    }

    if (hometownCityCode.value){
      console.log('hometownCityCode.value=', hometownCityCode.value)
      axios.get('/alumni/street/getStreetByCode?areaCode='+hometownCityCode.value).then(res => {
        console.log('hometownCityCode res =', res)
        if (res.data.success){
          picker.columns = []
          res.data.result.forEach(item => {
            picker.columns.push({
              text: item.name,
              value: item.name
            })
          })
          picker.val = [userForm.hometown]

          picker.key = Date.now();
          picker.show = true
          picker.type = type
        }
      })
    }
  }else if (type === 'gradeType'){
    picker.val = [userForm.gradeType]
    picker.columns = [{
      text: '初中部',
      value: '初中部'
    },{
      text: '高中部',
      value: '高中部'
    }]

    picker.key = Date.now();
    picker.show = true
    picker.type = type
  }
  // pickerVal.value = _.cloneDeep(pickerVal.value)
}

function isPhoneNumber(num) {
  let reg = /^1[3-9]\d{9}$/;
  return reg.test(num);
}

function btnQuit() {
  localStorage.removeItem('isLogin')
  // localStorage.removeItem('phone')
  localStorage.removeItem('isRegister')
  router.push('/login')
}

function btnSubmit() {
  // console.log('btnSubmit')
  if (!userForm.schoolName){
    showNotify({type: 'danger', message: '提示：学校不能为空', color: '#ad0000', background: '#ffe1e1'});
    return
  }
  if (!userForm.userName){
    showNotify({type: 'danger', message: '提示：姓名不能为空', color: '#ad0000', background: '#ffe1e1'});
    return
  }
  if (!userForm.sex){
    showNotify({type: 'danger', message: '提示：请选择性别', color: '#ad0000', background: '#ffe1e1'});
    return
  }
  if (!userForm.phone){
    showNotify({type: 'danger', message: '提示：手机号不能为空', color: '#ad0000', background: '#ffe1e1'});
    return
  }
  if (!isPhoneNumber(userForm.phone)){
    showNotify({type: 'danger', message: '提示：手机号不正确，请重新输入！', color: '#ad0000', background: '#ffe1e1'});
    return
  }
  if (!userForm.birthday){
    showNotify({type: 'danger', message: '提示：出生年月不能为空', color: '#ad0000', background: '#ffe1e1'});
    return
  }
  if (!userForm.yearOfGraduation){
    showNotify({type: 'danger', message: '提示：请选择毕业年份', color: '#ad0000', background: '#ffe1e1'});
    return
  }
  if (!userForm.gradeType){
    showNotify({type: 'danger', message: '提示：请选择所属学部', color: '#ad0000', background: '#ffe1e1'});
    return
  }
  if (!userForm.hometownCity){
    showNotify({type: 'danger', message: '提示：请选择老家所在区域', color: '#ad0000', background: '#ffe1e1'});
    return
  }
  if (!userForm.hometown){
    showNotify({type: 'danger', message: '提示：请选择老家所在街道/乡镇', color: '#ad0000', background: '#ffe1e1'});
    return
  }
  if (!userForm.class){
    showNotify({type: 'danger', message: '提示：请选择毕业班级', color: '#ad0000', background: '#ffe1e1'});
    return
  }
  // if (!userForm.isJoin){
  //   showNotify({type: 'danger', message: '提示：请选择是否现场参加2023年11月18日下午校友会成立大会', color: '#ad0000', background: '#ffe1e1'});
  //   return
  // }
  loading.value = true
  console.log('userform = ', userForm)
  const params = {
    // 大学名称
    colleague: userForm.schoolName,
    //用户名称
    userName: userForm.userName,
    // 性别
    sex: userForm.sex,
    // 手机号
    phone: userForm.phone,
    // 出生年月
    birth: userForm.birthday,
    // 毕业年份
    graduateYear: userForm.yearOfGraduation,
    // 班级
    classNo: userForm.class,
    // 年级
    gradeType: userForm.gradeType,
    // 老家所在街道/乡镇
    hometownAddress: userForm.hometown,
    // 老家所在区域
    hometownCity: userForm.hometownCity,
    // 毕业院校
    educationalBackground: userForm.schoolOfGraduation,
    // 最高学历
    highestEducation: userForm.highestEducation,
    // 现工作单位
    company: userForm.workUnit,
    // 职务
    post: userForm.workPost,
    // 主要从事的领域
    areaExpertise: userForm.engagedInField,
    // 工作区域
    companyCity: userForm.workArea,
    // 工作地址
    companyAddress: userForm.workAddress,
    // 居住所在区域
    city: userForm.residentialAddress,
    //是否现场参加2023年11月18日下午校友会成立大会
    // present: userForm.isJoin,
    // 其他建议
    advice: userForm.other,
  }
  console.log('params =', params)
  let url = '/alumni/mobile/register'
  if (userForm.id){
    url = '/alumni/mobile/modify'
    params.id = userForm.id
  }
  axios.post(url, params).then(res => {
    // alert(JSON.stringify(res))
    if (res.data && res.data.success) {
      localStorage.setItem('isRegister', '1')
      showNotify({type: 'success', message: userForm.id?'提示：保存成功！':'提示：注册成功！'});

      // 只有新用户第一次注册才会执行下面代码
      if (url === '/alumni/mobile/register' && res.data.result){
        localStorage.setItem('user', JSON.stringify(res.data.result))
        // 信息提交成功后，如果有[有效的活动]，自动跳转到活动报名页面
        axios.get('/alumni/mobile/effectCount').then(res => {
          console.log('effectCount res=', res)
          if (res.data.success){
            if (res.data.result > 0){
              router.push('/activity')
            }else{
              router.push('/')
            }
          }else{
            router.push('/')
          }
        })
      }

      // showDlg.value = true
    } else {
      showDialog({title: userForm.id?'保存失败！':'注册失败！', message: res.data.message});
    }
    loading.value = false
  })
}

function goto() {
  router.push('/')
}
</script>

<template>
  <div class="container">
    <div class="cover" v-if="props.showCover">
      <Image :src="cover" fit="contain" @click="goto"/>
    </div>
    <CellGroup inset class="aaa">
      <!-- 输入任意文本 -->
      <Field v-model="userForm.schoolName" label-class="zLabel"
             label="学校"
             readonly
             label-width="120"
             size="large"
             name="schoolName"
             required
             placeholder="请输入"
             input-align="right"/>
      <Field v-model="userForm.userName" label-class="zLabel"
             label="姓名"
             label-width="120"
             size="large"
             maxlength="10"
             name="userName"
             required
             placeholder="请输入"
             input-align="right"/>
      <Field v-model="userForm.sex" label-class="zLabel"
             label="性别"
             label-width="120"
             size="large"
             name="sex"
             required
             placeholder="请选择"
             input-align="right">
        <template #input>
          <RadioGroup v-model="userForm.sex" direction="horizontal">
            <Radio name="男">男</Radio>
            <Radio name="女">女</Radio>
          </RadioGroup>
        </template>
      </Field>
      <Field v-model="userForm.phone" label-class="zLabel"
             label="手机号"
             readonly
             label-width="120"
             maxlength="11"
             size="large"
             name="phone"
             required
             placeholder="请输入"
             input-align="right"/>
      <Field v-model="userForm.birthday" label-class="zLabel"
             label="出生年月"
             readonly
             is-link
             label-width="120"
             size="large"
             name="birthday"
             required
             placeholder="请选择"
             input-align="right" @click="clickPicker('birthday')"/>
      <Field v-model="userForm.gradeType" label-class="zLabel"
             label="所属学部"
             label-width="120"
             size="large"
             name="gradeType"
             required
             placeholder="请选择"
             input-align="right">
        <template #input>
          <RadioGroup v-model="userForm.gradeType" direction="horizontal">
            <Radio name="初中部">初中部</Radio>
            <Radio name="高中部">高中部</Radio>
          </RadioGroup>
        </template>
      </Field>
      <Field v-model="userForm.yearOfGraduation" label-class="zLabel"
             label="毕业年份"
             readonly
             label-width="120"
             size="large"
             name="yearOfGraduation"
             is-link
             required
             placeholder="请选择"
             input-align="right" @click="clickPicker('yearOfGraduation')"/>
      <Field v-model="userForm.class" label-class="zLabel"
             label="毕业班级"
             readonly
             label-width="120"
             size="large"
             is-link
             name="class"
             required
             placeholder="请选择"
             input-align="right" @click="clickPicker('class')"/>
      <Field v-model="userForm.hometownCity" label-class="zLabel"
             label="老家所在区域"
             label-width="120"
             is-link
             required
             readonly
             size="large"
             name="hometown"
             placeholder="请选择"
             input-align="right"  @click="clickArea('hometownCity')" />
      <Field v-model="userForm.hometown" label-class="zLabel"
             label="老家所在街道/乡镇"
             required
             is-link
             readonly
             label-width="150"
             size="large"
             name="hometown"
             placeholder="请选择"
             input-align="right" @click="clickPicker('hometown')"/>
      <Field v-model="userForm.schoolOfGraduation" label-class="zLabel"
             label="毕业院校"
             label-width="160"
             size="large"
             name="schoolOfGraduation"
             placeholder="请输入"
             input-align="right"/>
      <Field v-model="userForm.highestEducation" label-class="zLabel"
             label="最高学历"
             label-width="160"
             is-link
             readonly
             size="large"
             name="highestEducation"
             placeholder="请选择"
             input-align="right" @click="clickPicker('highestEducation')" />
      <Field v-model="userForm.workUnit" label-class="zLabel"
             label="工作单位"
             label-width="160"
             size="large"
             name="workUnit"
             placeholder="请输入"
             input-align="right"/>
      <Field v-model="userForm.workPost" label-class="zLabel"
             label="工作职务"
             label-width="160"
             size="large"
             name="workPost"
             placeholder="请输入"
             input-align="right"/>
      <Field v-model="userForm.workArea" label-class="zLabel"
             label="工作所在区域"
             is-link
             readonly
             size="large"
             name="workArea"
             placeholder="请选择"
             input-align="right" @click="clickArea('workArea')" />
      <Field v-model="userForm.workAddress" label-class="zLabel"
             label="工作详细地址"
             size="large"
             name="workAddress"
             placeholder="请输入"
             input-align="right"/>
      <Field v-model="userForm.engagedInField" label-class="zLabel"
             label="主要从事的领域"
             is-link
             readonly
             label-width="120"
             size="large"
             name="engagedInField"
             placeholder="请选择"
             input-align="right" @click="clickPicker('engagedInField')" />
      <Field v-model="userForm.residentialAddress" label-class="zLabel"
             label="居住所在区域"
             label-width="120"
             readonly
             size="large"
             name="residentialAddress"
             is-link
             placeholder="请选择"
             input-align="right" @click="clickArea('residentialAddress')" />
<!--      <Field v-model="userForm.isJoin" label-class="zLabel"-->
<!--             label="是否现场参加(地址和费用待定)2023年11月18日下午校友会成立大会"-->
<!--             label-width="160"-->
<!--             size="large"-->
<!--             name="isJoin"-->
<!--             required-->
<!--             placeholder="请选择"-->
<!--             input-align="right">-->
<!--        <template #input>-->
<!--          <RadioGroup v-model="userForm.isJoin" direction="horizontal">-->
<!--            <Radio name="是">是</Radio>-->
<!--            <Radio name="否">否</Radio>-->
<!--          </RadioGroup>-->
<!--        </template>-->
<!--      </Field>-->
      <Field v-model="userForm.other" label-class="zLabel"
             label="其他建议"
             type="textarea"
             rows="3"
             autosize
             size="large"
             name="other"
             placeholder="请输入"
             maxlength="100"
             show-word-limit
             input-align="right"/>
    </CellGroup>
    <div style="margin: 16px;display: flex;align-items: center;column-gap: 12px">
      <Button round block @click='btnQuit' style="width: 50%" v-if="props.showCover">
        退出
      </Button>
      <Button round block type="primary" color="#1D94F4" @click='btnSubmit' :loading="loading" style="width: 100%">
        保存
      </Button>
    </div>
    <Popup v-model:show="picker.show" round position="bottom" :key="picker.key">
      <Picker
          v-model="picker.val"
          title="请选择"
          :columns="picker.columns"
          @confirm="onConfirm"
          @cancel="onCancel"
      />
    </Popup>

    <Popup v-model:show="areaPicker.show" round position="bottom" :key="areaPicker.key">
      <Area
          v-model="areaPicker.val"
          :area-list="areaList"
          :columns-num="3"
          @confirm="onConfirmArea"
          @cancel="areaPicker.show = false"
      />
    </Popup>
  </div>
</template>
<style lang="postcss" scoped>
.container{
  padding-bottom: 50px;
  .zLabel {
    font-weight: bold !important;
  }
}

.cover{
  padding: 20px 20px 20px;
  display: flex;
  flex-flow: row;
  column-gap: 12px;
  background: linear-gradient(to top, #fff 0%, #97BBF3 100%);

}
</style>
