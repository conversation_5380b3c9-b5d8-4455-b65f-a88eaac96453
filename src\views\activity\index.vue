<script setup>
import {ref, onMounted, reactive} from 'vue'
import {Search, Button, Cell, showConfirmDialog, showDialog, showNotify, Image, List, Tab, Tabs, Dialog} from 'vant';
import btnOkImg from '@/assets/imgs/activity/btnOk.png'
import {useRouter} from "vue-router";
import axios from "@/utils/request";
import qs from 'qs'
import dayjs from "dayjs";
const router = useRouter()
const showDlg = ref(false)
const searchVal = ref('');
const showPicker = ref(false);
const active = ref('-2')
const loading = ref(false);
const finished = ref(false);
const page = reactive({
  pageNo: 0,
  pageSize: 20,
  total: 0
})

const current = reactive({
  list: []
})
const userInfo = reactive({})

onMounted(() => {
  const _userInfo = localStorage.getItem("user")
  if (_userInfo) {
    Object.assign(userInfo, JSON.parse(_userInfo))
  }else{
    localStorage.removeItem('isLogin')
    // localStorage.removeItem('phone')
    localStorage.removeItem('isRegister')
    router.push('/login')
    return
  }
  let input = document.querySelector('input');
  if (input){
    input.type = 'search';
  }
})

function gotoDetail(item) {
  console.log('gotoDetail=')
  const extra = {"id": item.id}
  let params = {
    "action": "click",
    "event": "activity_item",
    // 活动id
    "extra": JSON.stringify(extra),
    "module": "activity",
    "pageUrl": "/activity",
    "userId": userInfo.id
  }
  axios.post('/alumni/mobile/eventReport', params).then(res => {
    console.log('eventReport res=', res)
  })
  router.push('/activityDetail?id=' + item.id)
}

function btnRegister(item) {
  if (!item.hasApply) {
    if (userInfo.id) {
      let params = {
        activityId: item.id,
        userId: userInfo.id
      }
      axios.post('/alumni/mobile/apply', qs.stringify(params)).then(res => {
        console.log('apply res =', res)
        if (res.data.success) {
          showDlg.value = true
        } else if (res.data.message) {
          showNotify({type: 'primary', message: res.data.message})
        }
      })

      const extra = {"id": item.id}
      let params2 = {
        "action": "click",
        "event": "activity_item_apply",
        // 活动id
        "extra": JSON.stringify(extra),
        "module": "activity",
        "pageUrl": "/activity",
        "userId": userInfo.id
      }
      axios.post('/alumni/mobile/eventReport', params2).then(res => {
        console.log('eventReport res=', res)
      })
    }
    // if (!isDue(item)) {
    //
    // } else {
    //   showNotify({type: 'primary', message: '报名已截止'})
    // }
  } else {
    showNotify({type: 'primary', message: '您已报名'})
  }
}

function onLoad() {
  loading.value = true
  finished.value = false
  let params = {
    pageNo: page.pageNo + 1,
    pageSize: page.pageSize
  }
  if (active.value !== '-2') {
    params.status = active.value
  }
  if (userInfo.id) {
    params.userId = userInfo.id
  }
  if (searchVal.value) {
    params.activityName = `*${searchVal.value}*`
  }
  console.log('params =', params)
  axios.get("/alumni/mobile/myActivityList", {
    params: params
  }).then(res => {
    console.log('get activity list res:', res)
    // alert('员工数据：'+JSON.stringify(res.data.result))
    loading.value = false;
    if (res.data.success) {
      const result = res.data.result
      if (result) {
        // 数据全部加载完成
        if (current.list.length >= result.total) {
          finished.value = true;
        } else {
          page.pageNo = page.pageNo + 1
          current.list = current.list.concat(result.records)
        }
      } else {
        finished.value = true;
      }
    }
  })
}

function getStatusClass(item) {
  if (item.status === 0) {
    return 'top-right-green'
  }
  if (item.status === 1) {
    return 'top-right-red'
  }
  if (item.status === -1) {
    return 'top-right-gray'
  }
}

// 0 未开始  1进行中  -1已结束
function getStatusName(item) {
  if (item.status === 0) {
    return '报名中'
  }
  if (item.status === 1) {
    return '进行中'
  }
  if (item.status === -1) {
    return '已结束'
  }
}

function getYuan(item) {
  if (item.treatmentMoney === 0) {
    return '免费'
  } else if (item.isDutch) {
    return `￥${item.treatmentMoney}（AA制）`
  } else {
    return `￥${item.treatmentMoney}`
  }
}

function onChangeTab(name, title) {
  console.log('name=', name)
  page.pageNo = 0
  current.list = []
  onLoad()
}

function onSearch(value) {
  console.log('search =', value)
  searchVal.value = value
  page.pageNo = 0
  current.list = []
  onLoad()
}

// 是否已过期
function isDue(item) {
  console.log('is due item =', item.signInDeadline)
  const s = dayjs().isAfter(dayjs(item.signInDeadline))
  console.log('result=',s)
  return s
}

function btnOk() {
  showDlg.value = false
  page.pageNo = 0
  current.list = []
  onLoad()
}

</script>

<template>
  <div class="container">
    <Search v-model="searchVal" shape="round" placeholder="请输入活动名称关键字" @search="onSearch"/>
    <div class="tab-line">
      <Tabs v-model:active="active" @change="onChangeTab" :swipeable="true">
        <Tab title="全部" name="-2"/>
        <Tab title="报名中" name="0"/>
        <Tab title="进行中" name="1"/>
        <Tab title="已结束" name="-1"/>
      </Tabs>
    </div>
    <div class="card">
      <List
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad">
        <div class="item" @click="()=>gotoDetail(item)" v-for="item in current.list">
          <div class="header">
            <Image :src="item.activityCoverImageUrl" lazy-load show-loading radius="12px 12px 0 0"></Image>
            <div :class="['top-right', getStatusClass(item)]">
              {{ getStatusName(item) }}
            </div>
            <div class="bottom-left">
              {{ getYuan(item) }}
            </div>
          </div>
          <div class="footer">
            <h3 class="title">{{ item.activityName }}</h3>
            <div class="infos">
              <div class="info">
                报名截止：{{ item.signInDeadline }}
              </div>
              <div class="info">
                举办日期：{{ item.activityDate }}
              </div>
            </div>
            <div class="btn" v-if="[0,1].includes(item.status) && !isDue(item)">
              <Button block :type="item.hasApply? 'danger':'primary'" round :plain="item.hasApply" @click.stop="()=>btnRegister(item)">
                {{ item.hasApply ? '您已报名该活动' : '我要报名'}}
              </Button>
            </div>
          </div>
        </div>
      </List>
    </div>

    <Dialog v-model:show="showDlg" :showConfirmButton="false">
      <div class="dlg-body">
        <Image :src="btnOkImg" width="80"/>
        <div class="mid">恭喜您，报名成功</div>
        <Button block type="primary" round @click="btnOk">我知道了</Button>
      </div>
    </Dialog>
  </div>
</template>
<style scoped lang="postcss">
.container {
  .dlg-body{
    padding: 40px 30px 40px;
    display: flex;
    align-items: center;
    row-gap: 12px;
    flex-flow: column;
    font-size: 20px;
    font-weight: bold;
    .mid{
      padding-top: 10px;
      padding-bottom: 30px;
    }
  }
  :deep(.van-tab--active) {
    color: #1D94F4;
  }

  .tab-line {
    border-bottom: 1px solid #EFEFEF;
  }

  .card {
    padding: 20px;

    .item {
      box-shadow: 0 2px 6px rgba(0, 0, 0, .15);
      border-radius: 12px;
      margin-bottom: 20px;

      .footer {
        padding: 12px;

        .btn {
          padding: 12px 0 0;
        }

        .title {
          padding-bottom: 12px;
        }

        .infos {
          display: flex;
          flex-flow: column;
          row-gap: 6px;
          padding-bottom: 12px;

          .info {
            font-size: 16px;
            color: #72798C;
          }
        }

      }

      .header {
        position: relative;

        .bottom-left {
          background: #E15B66;
          position: absolute;
          bottom: 12px;
          left: 12px;
          padding: 2px 6px;
          color: #fff;
          font-size: 16px;
          //border-radius: 8px;
        }

        .top-right {
          position: absolute;
          top: 0;
          right: 0;
          padding: 6px 12px;
          border-radius: 12px 12px 0 12px;
          color: #fff;
          font-size: 16px;
        }

        .top-right-green {
          background: #00991F;
        }

        .top-right-red {
          background: #E15B66;
        }

        .top-right-gray {
          background: #848A9B;
        }
      }
    }
  }
}
</style>
