import axios from 'axios'

const axiosInstance = axios.create({
    baseURL: `${import.meta.env.VITE_API_ENDPOINT}`,
    withCredentials: true,
});

// 请求拦截器
axiosInstance.interceptors.request.use(
    function(config) {
        // 判断本地是否存在token
        let token = localStorage.getItem('token');
        if (token) {
            // 如果存在，则添加到请求头中
            config.headers['X-Access-Token'] = `${token}`
        }
        return config;
    },
    function(error) {
        // 对请求错误做些什么
        return Promise.reject(error);
    }
);

export default axiosInstance;
