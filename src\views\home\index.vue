<script setup>
import { useRouter } from "vue-router";
import { Field, CellGroup ,Picker, Popup, Area, Image, Button, Swipe, SwipeItem} from 'vant';
import bg from '@/assets/imgs/home/<USER>'
import coverTitle from '@/assets/imgs/home/<USER>'
import coverLogo from '@/assets/imgs/home/<USER>'
import img3 from '@/assets/imgs/home/<USER>/img3.png'
import img2 from '@/assets/imgs/home/<USER>/img2.png'
import btn1Img from '@/assets/imgs/home/<USER>'
import btn2Img from '@/assets/imgs/home/<USER>'
import btn3Img from '@/assets/imgs/home/<USER>'
import btn4Img from '@/assets/imgs/home/<USER>'
import btn5Img from '@/assets/imgs/home/<USER>'

import {ref, onMounted} from "vue";
import axios from "@/utils/request.js";
const router = useRouter()
const images = ref([img3, img2])
const isLogin = ref(false)
const isRegister = ref(false)

function goto(r) {
  router.push(r)
}

onMounted(()=>{
  const _isLogin = localStorage.getItem('isLogin');
  const _isRegister = localStorage.getItem('isRegister');
  if (_isLogin && _isLogin === '1'){
    isLogin.value = true
    // 判断是否有用户信息，如果没有，则获取
    const _userInfo = localStorage.getItem("user")
    if (!_userInfo) {
      const phone = localStorage.getItem('phone')
      if (phone){
        axios.get('/alumni/mobile/queryByMobile?phone='+phone).then(res => {
          console.log('query user info res=', res)
          if (res.data.success && res.data.result){
            // userData.data = res.data.result
            localStorage.setItem('user', JSON.stringify(res.data.result))
          }
        })
      }
    }
  }else{
    router.push('/login')
  }

  if (_isRegister && _isRegister === '1'){
    isRegister.value = true
  } else{
    router.push('/register')
  }
})

function goto2(e) {
  // console.log('goto2 =e',e)
  router.push("/activity")
}

</script>

<template>
  <div class="container">
    <div class="bg">
      <Image :src="bg" height="250" width="100%" fit="fill"/>
    </div>
    <div class="cover2">
      <Image :src="coverLogo"/>
      <Image :src="coverTitle"/>
    </div>
    <div class="swipe">
      <div class="swipe-content">
        <Swipe :autoplay="3000" lazy-render touchable indicator-color="#fff">
          <SwipeItem >
            <Image :src="img3" class="img-shadow"/>
          </SwipeItem>
          <SwipeItem @click="goto2">
            <Image :src="img2" class="img-shadow"/>
          </SwipeItem>
        </Swipe>
      </div>
<!--      <div class="zzc"></div>-->
    </div>
    <div class="grid">
      <div class="item" v-if="!isRegister">
        <Image :src="btn1Img"  @click="goto('/register')"/>
      </div>
      <div class="item">
        <Image :src="btn2Img" @click="goto('/userinfo')"/>
      </div>
      <div class="item">
        <Image :src="btn3Img" @click="goto('/schoolOrg')"/>
      </div>
      <div class="item2">
        <Image :src="btn5Img" @click="goto('/activity')" />
      </div>
    </div>
<!--   <h1 @click="goto">校友会</h1>-->
  </div>
</template>

<style lang="postcss" scoped>
.cover2{
  display: flex;
  align-items: center;
  column-gap: 12px;
  padding: 40px 20px 10px;
}
.grid{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  row-gap: 18px;
  column-gap: 12px;
  padding: 20px 20px ;
  .item{
    width: 48%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .item2{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.bg{
  position: absolute;
  top: 0;
  width: 100%;
  left: 0;
  right: 0;
}
.swipe{
  padding: 20px 20px;
  position: relative;
  .swipe-content{
    //padding: 12px;
    box-shadow: 0 2px 12px rgba(227, 242, 253, .85);
    border-radius: 12px;
  }
  .img-shadow{

    border-radius: 18px;
  }
  :deep(.van-swipe-item){
    line-height: 0;
  }
}

</style>
