<script setup>
import {ref, onMounted} from 'vue'
import {Grid, GridItem, Cell, CellGroup, Picker, Popup, Field, Image, Form, Button, showNotify} from 'vant';
import logo from '@/assets/imgs/login/logo.png'
import {useRouter} from "vue-router";
import img from '@/assets/imgs/login/img.png'
import axios from "@/utils/request";

const fieldValue = ref('');
const showPicker = ref(false);
const router = useRouter()
const phone = ref('')
const code = ref('611611')
const smsCode = ref('')

const status = ref('发送验证码')

onMounted(() => {
  const isLogin = localStorage.getItem('isLogin')
  const _phone = localStorage.getItem('phone')

  if (isLogin === '1') {
    router.push('/')
  }

  if (_phone){
    phone.value = _phone
  }
})

function isPhoneNumber(num) {
  let reg = /^1[3-9]\d{9}$/;
  return reg.test(num);
}

function onSubmit() {
  if (!isPhoneNumber(phone.value)) {
    showNotify({type: 'danger', message: '提示：手机号不正确！', color: '#ad0000', background: '#ffe1e1'});
    return;
  }
  if (!smsCode.value || smsCode.value.length !== 4) {
    showNotify({type: 'danger', message: '提示：验证码不正确！', color: '#ad0000', background: '#ffe1e1'});
    return;
  }
  axios.post('/alumni/mobile/login?authCode=' + smsCode.value + '&phone=' + phone.value).then(res => {
    console.log('login res=', res)
    if (res.data.success) {
      localStorage.setItem('phone', phone.value)
      localStorage.setItem('isLogin', '1')
      // 有值说明已经注册过了
      if (res.data.result) {
        localStorage.setItem('user', JSON.stringify(res.data.result))
        localStorage.setItem('isRegister', '1')
        router.push('/')
      } else {
        router.push('/register')
      }
    }else{
      if (res.data.message){
        showNotify({type: 'danger', message: res.data.message, color: '#ad0000', background: '#ffe1e1'});
      }else{
        showNotify({type: 'danger', message: '提示：登录失败！', color: '#ad0000', background: '#ffe1e1'});
      }
    }
  })
}

function btnSend() {
  if (!phone.value) {
    showNotify({type: 'danger', message: '提示：请输入手机号！', color: '#ad0000', background: '#ffe1e1'});
    return
  }
  if (status.value.includes('重新发送')){
    return
  }


  axios.post('/alumni/mobile/authCode?phone=' + phone.value).then(res => {
    console.log('authCode res=', res)
    if (res.data.success) {
      showNotify({type: 'success', message: '提示：验证码已发送，请注意查收！'});
      let t = 60
      const tm = setInterval(() => {
        t -= 1
        if (t === 1) {
          clearInterval(tm)
          status.value = `发送验证码`
        } else {
          status.value = `${t}s重新发送`
        }
      }, 1000)
    }else{
      if (res.data.message){
        showNotify({type: 'danger', message: res.data.message, color: '#ad0000', background: '#ffe1e1'});
      }else{
        showNotify({type: 'danger', message: '提示：验证码发送失败！', color: '#ad0000', background: '#ffe1e1'});
      }
    }
  })
}

</script>

<template>
  <div class="container">
    <div class="cover">
      <div class="up">
        <Image :src="logo" width="100"/>
      </div>
      <div class="down">
        <Image :src="img"/>
      </div>
    </div>
    <div class="content">
      <Form @submit="onSubmit">
        <CellGroup inset>
          <Field
              v-model="phone"
              size="large"
              label-width="80"
              name="手机号"
              label="手机号"
              maxlength="11"
              placeholder="请输入手机号"
              :rules="[{ required: true, message: '请输入手机号' }]"
          />
          <!--          <Field-->
          <!--              v-model="code"-->
          <!--              name="邀请码"-->
          <!--              size="large"-->
          <!--              label="邀请码"-->
          <!--              placeholder="邀请码"-->
          <!--              :rules="[{ required: true, message: '请填写邀请码' }]"-->
          <!--          />-->
          <Field
              v-model="smsCode"
              center
              label-width="80"
              maxlength="4"
              clearable
              label="验证码"
              placeholder="请输入验证码">
            <template #button>
              <Button size="small" type="primary" @click="btnSend" style="width:100px">{{ status }}</Button>
            </template>
          </Field>
        </CellGroup>
        <div style="margin: 16px;">
          <Button round block type="primary" native-type="submit">
            提交
          </Button>
        </div>
      </Form>
    </div>

  </div>
</template>

<style scoped lang="postcss">
.container {
  .cover {
    display: flex;
    flex-flow: column;
    align-items: center;
    padding: 40px 20px;
    row-gap: 48px;
    background: linear-gradient(25deg, #fff 0%, #fff 50%, #97BBF3 100%);

    .up {

    }

    .down {
      padding: 0 24px;
    }
  }
}
</style>
